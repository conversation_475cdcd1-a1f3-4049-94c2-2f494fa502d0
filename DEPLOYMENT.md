# 🚀 Deployment Guide

This guide walks you through deploying the Gemini Proxy Server to Vercel.

## Prerequisites

- [Vercel account](https://vercel.com/signup)
- [Google AI Studio API keys](https://aistudio.google.com/app/apikey)
- [Git repository](https://github.com) (GitHub, GitLab, or Bitbucket)

## Step 1: Get API Keys

1. **Get Gemini API Keys:**
   - Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - Click "Create API Key"
   - Create 2 API keys for load balancing
   - Save them securely

## Step 2: Set Up Vercel KV Database

1. **Create KV Database:**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Navigate to "Storage" → "Create Database"
   - Select "KV" (Key-Value store)
   - Choose a name (e.g., "gemini-proxy-kv")
   - Select region (choose closest to your users)
   - Click "Create"

2. **Get Connection Details:**
   - After creation, go to the database settings
   - Copy the `KV_REST_API_URL` and `KV_REST_API_TOKEN`
   - You'll need these for environment variables

## Step 3: Deploy to Vercel

### Option A: Deploy via Vercel Dashboard (Recommended)

1. **Connect Repository:**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your Git repository
   - Select the repository containing this code

2. **Configure Build Settings:**
   - Framework Preset: "Other"
   - Build Command: Leave empty (uses vercel.json)
   - Output Directory: Leave empty
   - Install Command: `bun install` (or `npm install`)

3. **Add Environment Variables:**
   - In project settings, go to "Environment Variables"
   - Add the following variables:

   | Variable | Value | Environment |
   |----------|-------|-------------|
   | `GEMINI_API_KEY1` | Your first API key | All |
   | `GEMINI_API_KEY2` | Your second API key | All |
   | `KV_REST_API_URL` | Your KV database URL | All |
   | `KV_REST_API_TOKEN` | Your KV database token | All |
   | `DEFAULT_MODEL` | `gemini-2.0-flash-exp` | All (optional) |

4. **Deploy:**
   - Click "Deploy"
   - Wait for deployment to complete
   - Your proxy will be available at `https://your-project.vercel.app`

### Option B: Deploy via Vercel CLI

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel:**
   ```bash
   vercel login
   ```

3. **Deploy:**
   ```bash
   # Initial deployment
   vercel

   # Follow prompts to set up project
   # Choose "N" for existing project
   # Enter project name
   # Choose directory (current)
   ```

4. **Set Environment Variables:**
   ```bash
   vercel env add GEMINI_API_KEY1
   vercel env add GEMINI_API_KEY2
   vercel env add KV_REST_API_URL
   vercel env add KV_REST_API_TOKEN
   vercel env add DEFAULT_MODEL
   ```

5. **Deploy to Production:**
   ```bash
   vercel --prod
   ```

## Step 4: Verify Deployment

1. **Test Status Endpoint:**
   ```bash
   curl https://your-project.vercel.app/api/status
   ```

2. **Test Chat Endpoint:**
   ```bash
   curl -X POST https://your-project.vercel.app/api/chat \
     -H "Content-Type: application/json" \
     -d '{"prompt": "Hello, world!", "stream": false}'
   ```

3. **Test Web Interface:**
   - Visit `https://your-project.vercel.app/test.html`
   - Try sending a message
   - Check status and models

## Step 5: Configure Custom Domain (Optional)

1. **Add Domain:**
   - In Vercel dashboard, go to project settings
   - Navigate to "Domains"
   - Add your custom domain
   - Follow DNS configuration instructions

2. **SSL Certificate:**
   - Vercel automatically provisions SSL certificates
   - Your API will be available at `https://your-domain.com`

## Troubleshooting

### Common Issues

1. **"API keys not configured" Error:**
   - Verify environment variables are set correctly
   - Check variable names match exactly
   - Ensure variables are set for all environments

2. **KV Database Connection Issues:**
   - Verify KV_REST_API_URL and KV_REST_API_TOKEN
   - Check KV database is in the same Vercel team
   - Ensure database is not paused

3. **Rate Limiting Issues:**
   - Check API key quotas in Google AI Studio
   - Verify keys are valid and not expired
   - Monitor usage in `/api/status` endpoint

4. **Build Failures:**
   - Check TypeScript compilation: `bun run lint`
   - Verify all dependencies are installed
   - Check vercel.json configuration

### Performance Optimization

1. **Edge Function Regions:**
   - Vercel automatically deploys to all regions
   - Monitor performance in Vercel Analytics
   - Consider regional KV databases for better performance

2. **Caching:**
   - The proxy includes intelligent caching
   - Monitor cache hit rates in status endpoint
   - Adjust rate limits if needed

3. **Monitoring:**
   - Set up Vercel Analytics
   - Monitor error rates and response times
   - Use `/api/status` for health checks

## Security Considerations

1. **API Key Security:**
   - Never expose API keys in client-side code
   - Use environment variables only
   - Rotate keys regularly

2. **Rate Limiting:**
   - The proxy handles Google's rate limits
   - Consider additional rate limiting for your users
   - Monitor usage patterns

3. **Access Control:**
   - Consider adding authentication
   - Use CORS headers appropriately
   - Monitor for abuse

## Maintenance

1. **Regular Updates:**
   - Keep dependencies updated
   - Monitor Google AI Studio for API changes
   - Update models list as new models are released

2. **Monitoring:**
   - Set up alerts for high error rates
   - Monitor API key usage
   - Track performance metrics

3. **Backup:**
   - KV data is automatically backed up by Vercel
   - Consider exporting usage statistics regularly
   - Document your configuration

## Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Review Vercel function logs
3. Test with the included test suite
4. Open an issue on GitHub

---

**🎉 Congratulations! Your Gemini Proxy Server is now deployed and ready to use.**
