<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Proxy Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .response {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            min-height: 100px;
        }
        .status {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>🤖 Gemini Proxy Test Interface</h1>
    
    <div class="container">
        <h2>Server Status</h2>
        <button onclick="checkStatus()">Check Status</button>
        <button onclick="listModels()">List Models</button>
        <div id="status" class="status">Click "Check Status" to see server status</div>
    </div>

    <div class="container">
        <h2>Chat Test</h2>
        <textarea id="prompt" placeholder="Enter your prompt here...">Write a short poem about artificial intelligence.</textarea>
        <br>
        <label>
            <input type="checkbox" id="streaming" checked> Enable Streaming
        </label>
        <br>
        <label>
            Model: 
            <select id="model">
                <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</option>
                <option value="gemini-2.0-flash-001">Gemini 2.0 Flash</option>
                <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
            </select>
        </label>
        <br>
        <button onclick="sendMessage()" id="sendBtn">Send Message</button>
        <button onclick="clearResponse()">Clear Response</button>
        
        <div id="response" class="response">Response will appear here...</div>
    </div>

    <script>
        let isStreaming = false;

        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                document.getElementById('status').innerHTML = `
<strong>Service:</strong> ${data.service || 'Unknown'}
<strong>Status:</strong> ${data.status || 'Unknown'}
<strong>Keys Available:</strong> ${data.keys?.available || 0}/${data.keys?.total || 0}
<strong>Timestamp:</strong> ${data.timestamp || 'Unknown'}

<strong>Key Usage:</strong>
${data.usage?.map(u => `
  Key ${u.keyIndex}: ${u.requestsPerMinute}/10 req/min, ${u.tokensPerMinute}/250k tokens/min, ${u.requestsPerDay}/250 req/day
  ${u.isBlocked ? '🚫 BLOCKED' : '✅ Available'}
`).join('') || 'No usage data'}
                `;
                document.getElementById('status').className = 'status success';
            } catch (error) {
                document.getElementById('status').innerHTML = `Error: ${error.message}`;
                document.getElementById('status').className = 'status error';
            }
        }

        async function listModels() {
            try {
                const response = await fetch('/api/models');
                const data = await response.json();
                
                document.getElementById('status').innerHTML = `
<strong>Available Models:</strong> ${data.count}
<strong>Default Model:</strong> ${data.default}

<strong>Models:</strong>
${data.models?.map(m => `
  • ${m.id} ${m.isDefault ? '(default)' : ''}
    ${m.description}
`).join('') || 'No models data'}
                `;
                document.getElementById('status').className = 'status success';
            } catch (error) {
                document.getElementById('status').innerHTML = `Error: ${error.message}`;
                document.getElementById('status').className = 'status error';
            }
        }

        async function sendMessage() {
            if (isStreaming) return;
            
            const prompt = document.getElementById('prompt').value.trim();
            if (!prompt) {
                alert('Please enter a prompt');
                return;
            }

            const streaming = document.getElementById('streaming').checked;
            const model = document.getElementById('model').value;
            const responseDiv = document.getElementById('response');
            const sendBtn = document.getElementById('sendBtn');
            
            isStreaming = true;
            sendBtn.disabled = true;
            sendBtn.textContent = 'Sending...';
            responseDiv.textContent = 'Generating response...';
            responseDiv.className = 'response';

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt,
                        stream: streaming,
                        model,
                    }),
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(`${response.status}: ${error.error}`);
                }

                if (streaming) {
                    await handleStreamingResponse(response, responseDiv);
                } else {
                    const data = await response.json();
                    responseDiv.textContent = data.text;
                    responseDiv.innerHTML += `\n\n<small>Model: ${data.model} | Tokens: ${data.usage?.totalTokens || 'unknown'}</small>`;
                }
                
                responseDiv.className = 'response success';
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
                responseDiv.className = 'response error';
            } finally {
                isStreaming = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send Message';
            }
        }

        async function handleStreamingResponse(response, responseDiv) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullText = '';

            responseDiv.textContent = '';

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.error) {
                                    throw new Error(data.error);
                                }
                                if (data.text) {
                                    fullText += data.text;
                                    responseDiv.textContent = fullText;
                                }
                                if (data.done) {
                                    return;
                                }
                            } catch (e) {
                                // Skip invalid JSON lines
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        }

        function clearResponse() {
            document.getElementById('response').textContent = 'Response will appear here...';
            document.getElementById('response').className = 'response';
        }

        // Load status on page load
        window.addEventListener('load', checkStatus);
    </script>
</body>
</html>
