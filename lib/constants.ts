// Constants for the Gemini Proxy Server

export const RATE_LIMITS = {
  requestsPerMinute: 10,
  tokensPerMinute: 250000,
  requestsPerDay: 250,
} as const;

export const DEFAULT_MODEL = 'gemini-2.0-flash-exp';

export const SUPPORTED_MODELS = [
  'gemini-2.0-flash-exp',
  'gemini-2.0-flash-001',
  'gemini-1.5-flash',
  'gemini-1.5-flash-001',
  'gemini-1.5-pro',
  'gemini-1.5-pro-001',
] as const;

export const KV_KEYS = {
  API_KEY_USAGE: (keyId: string) => `usage:${keyId}`,
  GLOBAL_STATS: 'global:stats',
} as const;

export const ERROR_CODES = {
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INVALID_API_KEY: 'INVALID_API_KEY',
  INVALID_REQUEST: 'INVALID_REQUEST',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  MODEL_NOT_SUPPORTED: 'MODEL_NOT_SUPPORTED',
  ALL_KEYS_BLOCKED: 'ALL_KEYS_BLOCKED',
} as const;

export const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Time constants in milliseconds
export const TIME = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
} as const;
