import { GoogleGenAI } from '@google/genai';
import type { ProxyRe<PERSON>, GeminiRequest, GeminiResponse, StreamChunk } from './types';
import { DEFAULT_MODEL, SUPPORTED_MODELS } from './constants';

export class GeminiClient {
  private client: GoogleGenAI;

  constructor(apiKey: string) {
    this.client = new GoogleGenAI({ apiKey });
  }

  /**
   * Generate content with streaming support
   */
  async generateContent(request: ProxyRequest): Promise<AsyncGenerator<StreamChunk, void, unknown>> {
    const model = request.model || DEFAULT_MODEL;
    
    if (!SUPPORTED_MODELS.includes(model as any)) {
      throw new Error(`Model ${model} is not supported`);
    }

    const geminiRequest: GeminiRequest = {
      contents: [
        {
          role: 'user',
          parts: [{ text: request.prompt }],
        },
      ],
      generationConfig: {
        temperature: request.temperature || 0.7,
        maxOutputTokens: request.maxTokens || 8192,
      },
    };

    if (request.stream) {
      return this.generateStreamingContent(model, geminiRequest);
    } else {
      return this.generateNonStreamingContent(model, geminiRequest);
    }
  }

  /**
   * Generate streaming content
   */
  private async *generateStreamingContent(
    model: string,
    request: GeminiRequest
  ): AsyncGenerator<StreamChunk, void, unknown> {
    try {
      const response = await this.client.models.generateContentStream({
        model,
        contents: request.contents,
        config: {
          temperature: request.generationConfig?.temperature,
          maxOutputTokens: request.generationConfig?.maxOutputTokens,
        },
      });

      for await (const chunk of response) {
        if (chunk.text) {
          yield {
            text: chunk.text,
            done: false,
          };
        }
      }

      yield {
        text: '',
        done: true,
      };
    } catch (error) {
      console.error('Error in streaming generation:', error);
      throw error;
    }
  }

  /**
   * Generate non-streaming content
   */
  private async *generateNonStreamingContent(
    model: string,
    request: GeminiRequest
  ): AsyncGenerator<StreamChunk, void, unknown> {
    try {
      const response = await this.client.models.generateContent({
        model,
        contents: request.contents,
        config: {
          temperature: request.generationConfig?.temperature,
          maxOutputTokens: request.generationConfig?.maxOutputTokens,
        },
      });

      yield {
        text: response.text || '',
        done: true,
      };
    } catch (error) {
      console.error('Error in non-streaming generation:', error);
      throw error;
    }
  }

  /**
   * Estimate token count for a prompt (rough estimation)
   */
  estimateTokenCount(text: string): number {
    // Rough estimation: ~4 characters per token for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Validate request format
   */
  validateRequest(request: any): ProxyRequest {
    if (!request.prompt || typeof request.prompt !== 'string') {
      throw new Error('Prompt is required and must be a string');
    }

    if (request.prompt.length > 100000) {
      throw new Error('Prompt is too long (max 100,000 characters)');
    }

    if (request.model && !SUPPORTED_MODELS.includes(request.model)) {
      throw new Error(`Model ${request.model} is not supported`);
    }

    if (request.temperature !== undefined) {
      if (typeof request.temperature !== 'number' || request.temperature < 0 || request.temperature > 2) {
        throw new Error('Temperature must be a number between 0 and 2');
      }
    }

    if (request.maxTokens !== undefined) {
      if (typeof request.maxTokens !== 'number' || request.maxTokens < 1 || request.maxTokens > 8192) {
        throw new Error('maxTokens must be a number between 1 and 8192');
      }
    }

    return {
      prompt: request.prompt,
      model: request.model || DEFAULT_MODEL,
      stream: Boolean(request.stream),
      temperature: request.temperature || 0.7,
      maxTokens: request.maxTokens || 8192,
    };
  }
}
