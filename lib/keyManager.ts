import { kv } from '@vercel/kv';
import type { ApiKeyUsage, KeyRotationResult, RateLimits } from './types';
import { RATE_LIMITS, KV_KEYS, TIME } from './constants';

export class KeyManager {
  private apiKeys: string[];
  private rateLimits: RateLimits;

  constructor(apiKeys: string[], rateLimits: RateLimits = RATE_LIMITS) {
    this.apiKeys = apiKeys;
    this.rateLimits = rateLimits;
  }

  /**
   * Get the best available API key based on current usage
   */
  async selectBestKey(): Promise<KeyRotationResult> {
    const keyUsages = await Promise.all(
      this.apiKeys.map((key, index) => this.getKeyUsage(`key_${index}`))
    );

    // Find the key with the lowest usage that's not blocked
    let bestKeyIndex = -1;
    let lowestScore = Infinity;

    for (let i = 0; i < keyUsages.length; i++) {
      const usage = keyUsages[i];

      if (!usage) continue;

      if (usage.isBlocked && usage.blockUntil && Date.now() < usage.blockUntil) {
        continue; // Skip blocked keys
      }

      // Calculate usage score (lower is better)
      const score = this.calculateUsageScore(usage);

      if (score < lowestScore) {
        lowestScore = score;
        bestKeyIndex = i;
      }
    }

    if (bestKeyIndex === -1) {
      throw new Error('All API keys are currently blocked due to rate limits');
    }

    return {
      selectedKey: this.apiKeys[bestKeyIndex]!,
      keyIndex: bestKeyIndex,
      usage: keyUsages[bestKeyIndex]!,
    };
  }

  /**
   * Calculate usage score for key selection (lower is better)
   */
  private calculateUsageScore(usage: ApiKeyUsage): number {
    const requestsScore = usage.requestsPerMinute / this.rateLimits.requestsPerMinute;
    const tokensScore = usage.tokensPerMinute / this.rateLimits.tokensPerMinute;
    const dailyScore = usage.requestsPerDay / this.rateLimits.requestsPerDay;
    
    // Weight recent usage more heavily
    return requestsScore * 0.5 + tokensScore * 0.3 + dailyScore * 0.2;
  }

  /**
   * Get current usage for a specific API key
   */
  async getKeyUsage(keyId: string): Promise<ApiKeyUsage> {
    try {
      const stored = await kv.get<ApiKeyUsage>(KV_KEYS.API_KEY_USAGE(keyId));
      
      if (!stored) {
        return this.createEmptyUsage(keyId);
      }

      // Reset counters if time windows have passed
      const now = Date.now();
      const resetUsage = { ...stored };

      // Reset minute counter if a minute has passed
      if (now - stored.lastMinuteReset > TIME.MINUTE) {
        resetUsage.requestsPerMinute = 0;
        resetUsage.tokensPerMinute = 0;
        resetUsage.lastMinuteReset = now;
      }

      // Reset daily counter if a day has passed
      if (now - stored.lastReset > TIME.DAY) {
        resetUsage.requestsPerDay = 0;
        resetUsage.lastReset = now;
      }

      // Clear block if time has passed
      if (resetUsage.isBlocked && resetUsage.blockUntil && now > resetUsage.blockUntil) {
        resetUsage.isBlocked = false;
        resetUsage.blockUntil = undefined;
      }

      // Update if anything changed
      if (JSON.stringify(resetUsage) !== JSON.stringify(stored)) {
        await kv.set(KV_KEYS.API_KEY_USAGE(keyId), resetUsage);
      }

      return resetUsage;
    } catch (error) {
      console.error(`Error getting usage for key ${keyId}:`, error);
      return this.createEmptyUsage(keyId);
    }
  }

  /**
   * Update usage after making a request
   */
  async updateKeyUsage(keyId: string, tokensUsed: number): Promise<void> {
    try {
      const usage = await this.getKeyUsage(keyId);
      
      usage.requestsPerMinute += 1;
      usage.requestsPerDay += 1;
      usage.tokensPerMinute += tokensUsed;

      // Check if we need to block the key
      if (
        usage.requestsPerMinute >= this.rateLimits.requestsPerMinute ||
        usage.tokensPerMinute >= this.rateLimits.tokensPerMinute ||
        usage.requestsPerDay >= this.rateLimits.requestsPerDay
      ) {
        usage.isBlocked = true;
        usage.blockUntil = Date.now() + TIME.MINUTE; // Block for 1 minute
      }

      await kv.set(KV_KEYS.API_KEY_USAGE(keyId), usage);
    } catch (error) {
      console.error(`Error updating usage for key ${keyId}:`, error);
    }
  }

  /**
   * Create empty usage object
   */
  private createEmptyUsage(keyId: string): ApiKeyUsage {
    const now = Date.now();
    return {
      keyId,
      requestsPerMinute: 0,
      tokensPerMinute: 0,
      requestsPerDay: 0,
      lastReset: now,
      lastMinuteReset: now,
      isBlocked: false,
    };
  }

  /**
   * Get all keys usage for monitoring
   */
  async getAllKeysUsage(): Promise<ApiKeyUsage[]> {
    return Promise.all(
      this.apiKeys.map((_, index) => this.getKeyUsage(`key_${index}`))
    );
  }

  /**
   * Reset usage for a specific key (for testing/admin purposes)
   */
  async resetKeyUsage(keyId: string): Promise<void> {
    await kv.set(KV_KEYS.API_KEY_USAGE(keyId), this.createEmptyUsage(keyId));
  }
}
