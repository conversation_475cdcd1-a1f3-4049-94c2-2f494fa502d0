// Types for the Gemini Proxy Server

export interface ProxyRequest {
  prompt: string;
  model?: string;
  stream?: boolean;
  maxTokens?: number;
  temperature?: number;
}

export interface ProxyResponse {
  text: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface StreamChunk {
  text: string;
  done: boolean;
}

export interface ApiKeyUsage {
  keyId: string;
  requestsPerMinute: number;
  tokensPerMinute: number;
  requestsPerDay: number;
  lastReset: number;
  lastMinuteReset: number;
  isBlocked: boolean;
  blockUntil?: number;
}

export interface RateLimits {
  requestsPerMinute: number;
  tokensPerMinute: number;
  requestsPerDay: number;
}

export interface KeyRotationResult {
  selectedKey: string;
  keyIndex: number;
  usage: ApiKeyUsage;
}

export interface ErrorResponse {
  error: string;
  code: string;
  details?: any;
}

// Gemini API specific types
export interface GeminiContent {
  role: 'user' | 'model';
  parts: Array<{
    text: string;
  }>;
}

export interface GeminiRequest {
  contents: GeminiContent[];
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
    topP?: number;
    topK?: number;
  };
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

// Environment variables type
export interface Environment {
  GEMINI_API_KEY1: string;
  GEMINI_API_KEY2: string;
  KV_REST_API_URL?: string;
  KV_REST_API_TOKEN?: string;
  DEFAULT_MODEL?: string;
}
