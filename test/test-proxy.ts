#!/usr/bin/env bun

/**
 * Test script for the Gemini Proxy Server
 * Run with: bun test/test-proxy.ts
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  duration?: number;
}

async function runTest(name: string, testFn: () => Promise<void>): Promise<TestResult> {
  const start = Date.now();
  try {
    await testFn();
    return {
      name,
      success: true,
      duration: Date.now() - start,
    };
  } catch (error: any) {
    return {
      name,
      success: false,
      error: error.message,
      duration: Date.now() - start,
    };
  }
}

async function testStatus() {
  const response = await fetch(`${BASE_URL}/api/status`);
  if (!response.ok) {
    throw new Error(`Status check failed: ${response.status}`);
  }
  
  const data = await response.json() as any;
  if (!data.service || !data.keys) {
    throw new Error('Invalid status response format');
  }

  console.log('✓ Status endpoint working');
  console.log(`  - Service: ${data.service}`);
  console.log(`  - Available keys: ${data.keys.available}/${data.keys.total}`);
}

async function testModels() {
  const response = await fetch(`${BASE_URL}/api/models`);
  if (!response.ok) {
    throw new Error(`Models check failed: ${response.status}`);
  }
  
  const data = await response.json() as any;
  if (!data.models || !Array.isArray(data.models)) {
    throw new Error('Invalid models response format');
  }

  console.log('✓ Models endpoint working');
  console.log(`  - Available models: ${data.count}`);
  console.log(`  - Default model: ${data.default}`);
}

async function testNonStreamingChat() {
  const response = await fetch(`${BASE_URL}/api/chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt: 'Say hello in exactly 5 words.',
      stream: false,
    }),
  });
  
  if (!response.ok) {
    const error = await response.json() as any;
    throw new Error(`Chat failed: ${response.status} - ${error.error}`);
  }
  
  const data = await response.json() as any;
  if (!data.text) {
    throw new Error('No text in response');
  }

  console.log('✓ Non-streaming chat working');
  console.log(`  - Response: ${data.text.substring(0, 50)}...`);
  console.log(`  - Model: ${data.model}`);
  console.log(`  - Tokens: ${data.usage?.totalTokens || 'unknown'}`);
}

async function testStreamingChat() {
  const response = await fetch(`${BASE_URL}/api/chat`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt: 'Count from 1 to 5, one number per line.',
      stream: true,
    }),
  });
  
  if (!response.ok) {
    const error = await response.json() as any;
    throw new Error(`Streaming chat failed: ${response.status} - ${error.error}`);
  }
  
  if (!response.body) {
    throw new Error('No response body for streaming');
  }
  
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let chunks = 0;
  let fullText = '';
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.slice(6));
          if (data.text) {
            fullText += data.text;
            chunks++;
          }
          if (data.done) {
            console.log('✓ Streaming chat working');
            console.log(`  - Chunks received: ${chunks}`);
            console.log(`  - Full response: ${fullText.substring(0, 50)}...`);
            return;
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
  
  throw new Error('Stream ended without done signal');
}

async function testRateLimiting() {
  console.log('Testing rate limiting (this may take a moment)...');
  
  // Make multiple rapid requests to test rate limiting
  const promises = Array.from({ length: 15 }, (_, i) =>
    fetch(`${BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: `Test request ${i + 1}`,
        stream: false,
      }),
    })
  );
  
  const responses = await Promise.all(promises);
  const rateLimited = responses.filter(r => r.status === 429);
  
  console.log('✓ Rate limiting test completed');
  console.log(`  - Total requests: ${responses.length}`);
  console.log(`  - Rate limited: ${rateLimited.length}`);
  console.log(`  - Successful: ${responses.filter(r => r.ok).length}`);
}

async function main() {
  console.log('🚀 Starting Gemini Proxy Server Tests\n');
  
  const tests = [
    () => testStatus(),
    () => testModels(),
    () => testNonStreamingChat(),
    () => testStreamingChat(),
    () => testRateLimiting(),
  ];
  
  const results: TestResult[] = [];
  
  for (const [index, test] of tests.entries()) {
    const testName = test.name.replace('test', '').replace(/([A-Z])/g, ' $1').trim();
    console.log(`\n${index + 1}. Testing ${testName}...`);
    
    const result = await runTest(testName, test);
    results.push(result);
    
    if (!result.success) {
      console.log(`❌ ${result.name} failed: ${result.error}`);
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => r.success === false).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⏱️  Total time: ${results.reduce((sum, r) => sum + (r.duration || 0), 0)}ms`);
  
  if (failed > 0) {
    console.log('\nFailed tests:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.name}: ${r.error}`);
    });
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed!');
  }
}

if (import.meta.main) {
  main().catch(console.error);
}
