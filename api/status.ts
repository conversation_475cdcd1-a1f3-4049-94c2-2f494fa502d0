import { KeyManager } from '../lib/keyManager';
import type { Environment } from '../lib/types';
import { ERROR_CODES, HTTP_STATUS } from '../lib/constants';

// This runs on Vercel Edge Runtime
export const runtime = 'edge';

export async function GET(req: Request) {
  try {
    // Get environment variables
    const env: Environment = {
      GEMINI_API_KEY1: process.env.GEMINI_API_KEY1!,
      GEMINI_API_KEY2: process.env.GEMINI_API_KEY2!,
      KV_REST_API_URL: process.env.KV_REST_API_URL,
      KV_REST_API_TOKEN: process.env.KV_REST_API_TOKEN,
      DEFAULT_MODEL: process.env.DEFAULT_MODEL,
    };

    // Validate environment
    if (!env.GEMINI_API_KEY1 || !env.GEMINI_API_KEY2) {
      throw new Error('API keys not configured');
    }

    // Initialize key manager
    const keyManager = new KeyManager([env.GEMINI_API_KEY1, env.GEMINI_API_KEY2]);

    // Get usage for all keys
    const allUsages = await keyManager.getAllKeysUsage();

    // Calculate overall status
    const availableKeys = allUsages.filter(usage => !usage.isBlocked).length;
    const totalKeys = allUsages.length;

    const status = {
      timestamp: new Date().toISOString(),
      service: 'Gemini Proxy Server',
      version: '1.0.0',
      status: availableKeys > 0 ? 'healthy' : 'degraded',
      keys: {
        total: totalKeys,
        available: availableKeys,
        blocked: totalKeys - availableKeys,
      },
      usage: allUsages.map((usage, index) => ({
        keyId: usage.keyId,
        keyIndex: index,
        requestsPerMinute: usage.requestsPerMinute,
        tokensPerMinute: usage.tokensPerMinute,
        requestsPerDay: usage.requestsPerDay,
        isBlocked: usage.isBlocked,
        blockUntil: usage.blockUntil ? new Date(usage.blockUntil).toISOString() : null,
        lastReset: new Date(usage.lastReset).toISOString(),
        lastMinuteReset: new Date(usage.lastMinuteReset).toISOString(),
      })),
      rateLimits: {
        requestsPerMinute: 10,
        tokensPerMinute: 250000,
        requestsPerDay: 250,
      },
    };

    return Response.json(status);

  } catch (error: any) {
    console.error('Error in status handler:', error);

    return Response.json(
      {
        error: error.message || 'Internal server error',
        code: ERROR_CODES.INTERNAL_ERROR,
        timestamp: new Date().toISOString(),
        service: 'Gemini Proxy Server',
        status: 'error',
      },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    );
  }
}