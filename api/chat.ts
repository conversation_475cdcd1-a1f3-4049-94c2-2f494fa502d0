import { KeyManager } from '../lib/keyManager';
import { GeminiClient } from '../lib/geminiClient';
import type { ProxyRequest, Environment } from '../lib/types';
import { ERROR_CODES, HTTP_STATUS } from '../lib/constants';

// This runs on Vercel Edge Runtime
export const runtime = 'edge';

export async function POST(req: Request) {
  try {
    // Get environment variables
    const env: Environment = {
      GEMINI_API_KEY1: process.env.GEMINI_API_KEY1!,
      GEMINI_API_KEY2: process.env.GEMINI_API_KEY2!,
      KV_REST_API_URL: process.env.KV_REST_API_URL,
      KV_REST_API_TOKEN: process.env.KV_REST_API_TOKEN,
      DEFAULT_MODEL: process.env.DEFAULT_MODEL,
    };

    // Validate environment
    if (!env.GEMINI_API_KEY1 || !env.GEMINI_API_KEY2) {
      throw new Error('API keys not configured');
    }

    // Parse request body
    const body = await req.json();

    // Initialize key manager
    const keyManager = new KeyManager([env.GEMINI_API_KEY1, env.GEMINI_API_KEY2]);

    // Select best API key
    const keyResult = await keyManager.selectBestKey();
    const keyId = `key_${keyResult.keyIndex}`;

    // Initialize Gemini client with selected key
    const geminiClient = new GeminiClient(keyResult.selectedKey);

    // Validate and parse request
    const proxyRequest: ProxyRequest = geminiClient.validateRequest(body);

    // Estimate token usage for tracking
    const estimatedTokens = geminiClient.estimateTokenCount(proxyRequest.prompt);

    // Check if streaming is requested
    if (proxyRequest.stream) {
      return handleStreamingRequest(geminiClient, proxyRequest, keyManager, keyId, estimatedTokens);
    } else {
      return handleNonStreamingRequest(geminiClient, proxyRequest, keyManager, keyId, estimatedTokens);
    }

  } catch (error: any) {
    console.error('Error in chat handler:', error);

    // Handle specific error types
    if (error.message.includes('rate limit') || error.message.includes('quota')) {
      return Response.json(
        {
          error: 'Rate limit exceeded. Please try again later.',
          code: ERROR_CODES.RATE_LIMIT_EXCEEDED
        },
        { status: HTTP_STATUS.TOO_MANY_REQUESTS }
      );
    }

    if (error.message.includes('API key')) {
      return Response.json(
        {
          error: 'Invalid API key',
          code: ERROR_CODES.INVALID_API_KEY
        },
        { status: HTTP_STATUS.UNAUTHORIZED }
      );
    }

    if (error.message.includes('blocked') || error.message.includes('All API keys')) {
      return Response.json(
        {
          error: 'All API keys are currently rate limited. Please try again later.',
          code: ERROR_CODES.ALL_KEYS_BLOCKED
        },
        { status: HTTP_STATUS.TOO_MANY_REQUESTS }
      );
    }

    return Response.json(
      {
        error: error.message || 'Internal server error',
        code: ERROR_CODES.INTERNAL_ERROR
      },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    );
  }
}

async function handleStreamingRequest(
  geminiClient: GeminiClient,
  proxyRequest: ProxyRequest,
  keyManager: KeyManager,
  keyId: string,
  estimatedTokens: number
) {
  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const generator = await geminiClient.generateContent(proxyRequest);
        let totalTokens = 0;

        for await (const chunk of generator) {
          // Estimate tokens in this chunk
          const chunkTokens = geminiClient.estimateTokenCount(chunk.text);
          totalTokens += chunkTokens;

          // Send chunk to client
          const data = `data: ${JSON.stringify(chunk)}\n\n`;
          controller.enqueue(encoder.encode(data));

          if (chunk.done) {
            // Update usage tracking
            await keyManager.updateKeyUsage(keyId, totalTokens);
            controller.close();
            break;
          }
        }
      } catch (error: any) {
        console.error('Streaming error:', error);
        const errorChunk = {
          text: '',
          done: true,
          error: error.message,
        };
        const data = `data: ${JSON.stringify(errorChunk)}\n\n`;
        controller.enqueue(encoder.encode(data));
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

async function handleNonStreamingRequest(
  geminiClient: GeminiClient,
  proxyRequest: ProxyRequest,
  keyManager: KeyManager,
  keyId: string,
  estimatedTokens: number
) {
  try {
    const generator = await geminiClient.generateContent(proxyRequest);
    let fullText = '';
    let totalTokens = 0;

    for await (const chunk of generator) {
      fullText += chunk.text;
      totalTokens += geminiClient.estimateTokenCount(chunk.text);

      if (chunk.done) {
        break;
      }
    }

    // Update usage tracking
    await keyManager.updateKeyUsage(keyId, totalTokens);

    return Response.json({
      text: fullText,
      model: proxyRequest.model,
      usage: {
        promptTokens: estimatedTokens,
        completionTokens: totalTokens,
        totalTokens: estimatedTokens + totalTokens,
      },
    });

  } catch (error: any) {
    throw error; // Re-throw to be handled by main error handler
  }
}