import { SUPPORTED_MODELS, DEFAULT_MODEL } from '../lib/constants';
import { ERROR_CODES } from '../lib/constants';

// This runs on Vercel Edge Runtime
export const runtime = 'edge';

export async function GET() {
  try {
    const models = SUPPORTED_MODELS.map(model => ({
      id: model,
      name: model,
      description: getModelDescription(model),
      isDefault: model === DEFAULT_MODEL,
    }));

    return Response.json({
      models,
      default: DEFAULT_MODEL,
      count: models.length,
    });

  } catch (error: any) {
    console.error('Error in models handler:', error);

    return Response.json(
      {
        error: error.message || 'Internal server error',
        code: ERROR_CODES.INTERNAL_ERROR
      },
      { status: 500 }
    );
  }
}

function getModelDescription(model: string): string {
  const descriptions: Record<string, string> = {
    'gemini-2.0-flash-exp': 'Gemini 2.0 Flash (Experimental) - Latest experimental model with enhanced capabilities',
    'gemini-2.0-flash-001': 'Gemini 2.0 Flash - Fast and efficient model for most tasks',
    'gemini-1.5-flash': 'Gemini 1.5 Flash - Fast and efficient model',
    'gemini-1.5-flash-001': 'Gemini 1.5 Flash (001) - Stable version of Gemini 1.5 Flash',
    'gemini-1.5-pro': 'Gemini 1.5 Pro - More capable model for complex tasks',
    'gemini-1.5-pro-001': 'Gemini 1.5 Pro (001) - Stable version of Gemini 1.5 Pro',
  };

  return descriptions[model] || 'Gemini model for text generation';
}