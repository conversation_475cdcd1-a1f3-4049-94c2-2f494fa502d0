{"name": "gemini-proxy", "version": "1.0.0", "description": "High-performance Gemini 2.5 Flash proxy server with intelligent key rotation and streaming support", "module": "index.ts", "type": "module", "private": true, "scripts": {"vercel:dev": "vercel dev", "dev": "bun test/test-proxy.ts", "build": "tsc --noEmit", "deploy": "vercel --prod", "test": "bun test/test-proxy.ts", "test:prod": "TEST_BASE_URL=https://your-proxy.vercel.app bun test/test-proxy.ts", "lint": "tsc --noEmit", "clean": "rm -rf .vercel"}, "keywords": ["gemini", "ai", "proxy", "vercel", "edge-functions", "streaming", "rate-limiting", "google-ai"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/gemini-proxy.git"}, "bugs": {"url": "https://github.com/your-username/gemini-proxy/issues"}, "homepage": "https://github.com/your-username/gemini-proxy#readme", "engines": {"node": ">=18.0.0"}, "devDependencies": {"@types/bun": "^1.2.19", "@types/node": "^24.1.0", "typescript": "^5.9.2"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@google/genai": "^1.12.0", "@vercel/kv": "^3.0.0", "ai": "^5.0.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}