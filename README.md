# 🤖 Gemini 2.5 Flash Proxy Server

A high-performance proxy server for Google's Gemini 2.5 Flash API, built for Vercel Edge Functions with intelligent key rotation, rate limiting, and streaming support.

## ✨ Features

- **🚀 Vercel Edge Functions**: Deploy globally with minimal latency
- **🔄 Smart Key Rotation**: Intelligent load balancing across multiple API keys
- **📊 Rate Limit Management**: Handles 10 req/min, 250k tokens/min, 250 req/day per key
- **⚡ Streaming Support**: Real-time response streaming
- **💾 Usage Tracking**: Vercel KV integration for persistent usage monitoring
- **🛡️ Error Handling**: Graceful fallbacks and comprehensive error responses
- **📈 Monitoring**: Built-in status and usage monitoring endpoints

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │───▶│  Vercel Edge     │───▶│  Gemini API     │
│                 │    │  Functions       │    │  (Google AI)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Vercel KV      │
                       │ (Usage Tracking) │
                       └──────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ or Bun
- Vercel account
- Google AI Studio API keys ([Get them here](https://aistudio.google.com/app/apikey))
- Vercel KV database ([Create one here](https://vercel.com/dashboard/stores))

### Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd gemini-proxy

# Install dependencies
bun install

# Copy environment variables
cp .env.example .env
```

### Environment Setup

Edit `.env` with your credentials:

```env
# Gemini API Keys (get from https://aistudio.google.com/app/apikey)
GEMINI_API_KEY1=your_first_gemini_api_key_here
GEMINI_API_KEY2=your_second_gemini_api_key_here

# Vercel KV Database (create at https://vercel.com/dashboard/stores)
KV_REST_API_URL=your_vercel_kv_rest_api_url
KV_REST_API_TOKEN=your_vercel_kv_rest_api_token

# Optional: Custom model (defaults to gemini-2.0-flash-exp)
DEFAULT_MODEL=gemini-2.0-flash-exp
```

### Local Development

```bash
# Start development server
bun dev

# Or with Vercel CLI
vercel dev

# Run tests
bun test/test-proxy.ts

# Test in browser
open http://localhost:3000/test.html
```

## 📡 API Endpoints

### POST `/api/chat`

Generate text using Gemini models with optional streaming.

**Request Body:**
```json
{
  "prompt": "Your prompt here",
  "stream": true,
  "model": "gemini-2.0-flash-exp",
  "temperature": 0.7,
  "maxTokens": 8192
}
```

**Response (Non-streaming):**
```json
{
  "text": "Generated response...",
  "model": "gemini-2.0-flash-exp",
  "usage": {
    "promptTokens": 10,
    "completionTokens": 50,
    "totalTokens": 60
  }
}
```

**Response (Streaming):**
```
data: {"text": "Generated", "done": false}
data: {"text": " response...", "done": false}
data: {"text": "", "done": true}
```

### GET `/api/status`

Get server status and API key usage information.

**Response:**
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "Gemini Proxy Server",
  "status": "healthy",
  "keys": {
    "total": 2,
    "available": 2,
    "blocked": 0
  },
  "usage": [
    {
      "keyId": "key_0",
      "requestsPerMinute": 5,
      "tokensPerMinute": 1250,
      "requestsPerDay": 45,
      "isBlocked": false
    }
  ]
}
```

### GET `/api/models`

List available Gemini models.

**Response:**
```json
{
  "models": [
    {
      "id": "gemini-2.0-flash-exp",
      "name": "gemini-2.0-flash-exp",
      "description": "Gemini 2.0 Flash (Experimental)",
      "isDefault": true
    }
  ],
  "default": "gemini-2.0-flash-exp",
  "count": 6
}
```

## 🚀 Deployment

### Deploy to Vercel

1. **Fork/Clone this repository**

2. **Set up Vercel KV Database:**
   ```bash
   # Create KV database in Vercel dashboard
   # Copy the connection details
   ```

3. **Deploy to Vercel:**
   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Deploy
   vercel

   # Set environment variables
   vercel env add GEMINI_API_KEY1
   vercel env add GEMINI_API_KEY2
   vercel env add KV_REST_API_URL
   vercel env add KV_REST_API_TOKEN
   ```

4. **Or use Vercel Dashboard:**
   - Connect your GitHub repository
   - Add environment variables in project settings
   - Deploy automatically on push

### Environment Variables in Vercel

Set these in your Vercel project settings:

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY1` | First Gemini API key | ✅ |
| `GEMINI_API_KEY2` | Second Gemini API key | ✅ |
| `KV_REST_API_URL` | Vercel KV REST API URL | ✅ |
| `KV_REST_API_TOKEN` | Vercel KV REST API Token | ✅ |
| `DEFAULT_MODEL` | Default Gemini model | ❌ |

## 💡 Usage Examples

### JavaScript/TypeScript

```javascript
// Non-streaming request
const response = await fetch('https://your-proxy.vercel.app/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Explain quantum computing in simple terms',
    stream: false,
    model: 'gemini-2.0-flash-exp'
  })
});

const data = await response.json();
console.log(data.text);
```

```javascript
// Streaming request
const response = await fetch('https://your-proxy.vercel.app/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Write a story about AI',
    stream: true
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;

  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.slice(6));
      if (data.text) {
        process.stdout.write(data.text);
      }
      if (data.done) break;
    }
  }
}
```

### Python

```python
import requests
import json

# Non-streaming
response = requests.post('https://your-proxy.vercel.app/api/chat',
  json={
    'prompt': 'What is machine learning?',
    'stream': False
  }
)
print(response.json()['text'])

# Streaming
response = requests.post('https://your-proxy.vercel.app/api/chat',
  json={
    'prompt': 'Explain neural networks',
    'stream': True
  },
  stream=True
)

for line in response.iter_lines():
  if line.startswith(b'data: '):
    data = json.loads(line[6:])
    if data.get('text'):
      print(data['text'], end='')
    if data.get('done'):
      break
```

### cURL

```bash
# Non-streaming
curl -X POST https://your-proxy.vercel.app/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Hello, how are you?",
    "stream": false
  }'

# Streaming
curl -X POST https://your-proxy.vercel.app/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Count from 1 to 10",
    "stream": true
  }' \
  --no-buffer
```

## 🔧 Configuration

### Rate Limits

The proxy handles Google AI Studio's rate limits:

- **10 requests per minute** per API key
- **250,000 tokens per minute** per API key
- **250 requests per day** per API key

### Supported Models

- `gemini-2.0-flash-exp` (default)
- `gemini-2.0-flash-001`
- `gemini-1.5-flash`
- `gemini-1.5-flash-001`
- `gemini-1.5-pro`
- `gemini-1.5-pro-001`

### Key Rotation Algorithm

The proxy uses an intelligent scoring system:
- **50%** weight on requests per minute usage
- **30%** weight on tokens per minute usage
- **20%** weight on daily requests usage

Keys are automatically blocked when limits are reached and unblocked when the time window resets.

## 🧪 Testing

### Run Test Suite

```bash
# Run all tests
bun test/test-proxy.ts

# Test against deployed version
TEST_BASE_URL=https://your-proxy.vercel.app bun test/test-proxy.ts
```

### Manual Testing

1. **Browser Interface**: Visit `/test.html` for interactive testing
2. **Status Check**: Visit `/api/status` to monitor key usage
3. **Models List**: Visit `/api/models` to see available models

## 🛠️ Development

### Project Structure

```
gemini-proxy/
├── api/                    # Vercel Edge Functions
│   ├── chat.ts            # Main chat endpoint
│   ├── status.ts          # Status monitoring
│   └── models.ts          # Model listing
├── lib/                   # Core libraries
│   ├── types.ts           # TypeScript types
│   ├── constants.ts       # Configuration constants
│   ├── keyManager.ts      # Key rotation logic
│   └── geminiClient.ts    # Gemini API client
├── test/                  # Test files
│   └── test-proxy.ts      # Test suite
├── public/                # Static files
│   └── test.html          # Test interface
├── vercel.json            # Vercel configuration
└── package.json           # Dependencies
```

### Adding New Features

1. **New Endpoints**: Add files to `/api/` directory
2. **Key Management**: Modify `lib/keyManager.ts`
3. **Client Logic**: Update `lib/geminiClient.ts`
4. **Types**: Add to `lib/types.ts`

## 🚨 Error Handling

The proxy provides comprehensive error handling:

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `RATE_LIMIT_EXCEEDED` | 429 | API key rate limit reached |
| `INVALID_API_KEY` | 401 | Invalid or expired API key |
| `INVALID_REQUEST` | 400 | Malformed request |
| `MODEL_NOT_SUPPORTED` | 400 | Unsupported model specified |
| `ALL_KEYS_BLOCKED` | 429 | All API keys are rate limited |
| `INTERNAL_ERROR` | 500 | Server error |

## 📊 Monitoring

### Built-in Monitoring

- **Status Endpoint**: Real-time key usage and health
- **Usage Tracking**: Persistent storage in Vercel KV
- **Error Logging**: Comprehensive error tracking
- **Performance Metrics**: Request duration and token usage

### External Monitoring

Consider integrating with:
- **Vercel Analytics**: Built-in performance monitoring
- **Sentry**: Error tracking and performance monitoring
- **DataDog**: Custom metrics and alerting
- **Grafana**: Custom dashboards

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Google AI Studio](https://aistudio.google.com/) for the Gemini API
- [Vercel](https://vercel.com/) for Edge Functions and KV storage
- [Bun](https://bun.com/) for the fast JavaScript runtime

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/gemini-proxy/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/gemini-proxy/discussions)
- **Email**: <EMAIL>

---

**⚡ Built with Vercel Edge Functions for maximum performance and global distribution.**
